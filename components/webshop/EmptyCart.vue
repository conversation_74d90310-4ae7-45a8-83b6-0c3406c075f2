<template>
	<div class="empty-cart">
		<div class="wrapper">
			<div class="empty-cart-title" v-interpolation>
				<BaseCmsLabel code="empty_shopping_cart" tag="div" />
			</div>
			<!-- <NuxtLink class="btn" :to="getAppUrl('auth_login')">Prijavi se</NuxtLink>-->

			<div class="popular-categories">
				<div class="popular-categories-title"><BaseCmsLabel code="popular_categories" />:</div>
				<CatalogCategories />
			</div>
		</div>

		
		
		<BaseCatalogWishlist thumb-preset="catalogEntry" v-slot="{items}">
			<div class="cw" v-if="items?.length">
				<div class="wrapper">
					<CmsSectionHeader>
						<BaseCmsLabel code="wishlist" />
						<template #btn>
							<NuxtLink :to="getAppUrl('wishlist')" class="widget-btn-show-all">
								<span><BaseCmsLabel code="show_all" /></span>
							</NuxtLink>
						</template>
					</CmsSectionHeader>
				</div>
				<UiSwiper
					name="hp-special-offers"
					:options="{
						enabled: false,
						slidesPerView: 5,
						slidesPerGroup: 5,
						spaceBetween: 24,
						breakpoints: {
							980: {
								enabled: true,
							}
						}
					}">
					<BaseUiSwiperSlide v-for="(item, index) in items" :key="item.id" class="slide">
						<CatalogIndexEntry :item="item" :index="index" />
					</BaseUiSwiperSlide>
				</UiSwiper>
			</div>
		</BaseCatalogWishlist>

		<BaseCatalogProductsWidget :fetch="{sort: 'last_view', special_view: 'viewed', id_exclude: item.id}" :cache="false" v-slot="{items}"></BaseCatalogProductsWidget>
	</div>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
</script>

<style scoped lang="less">
	.empty-cart{padding: 60px 0;}
	:deep(h2){font-size: 42px; font-weight: bold; padding: 0 0 15px;}
	.empty-cart-title{padding-bottom: 10px; text-align: center;}
	.btn{min-width: 300px;}
	.popular-categories{padding: 85px 0 0;}
	.popular-categories-title{font-size: 28px; font-weight: bold; padding: 0 0 25px; text-align: center;}
	:deep(.categories){margin: 0;}

	.cw{padding: 90px 0 0; position: relative;}
	:deep(.swiper-container){display: flex;}
	:deep(.swiper){overflow: initial;}	
</style>